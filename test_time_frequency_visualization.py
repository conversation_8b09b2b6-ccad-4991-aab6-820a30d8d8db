#!/usr/bin/env python3
"""
🎯 时间/频域变换分析可视化测试脚本
Test script for Time/Frequency Domain Transformation Analysis Visualization

运行此脚本来生成深度学习模型动态抗噪能力的综合可视化演示
"""

import sys
import os
import numpy as np
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt

# 添加src路径到Python路径
sys.path.append('src')

def main():
    """主函数"""
    print("🎯 时间/频域变换分析可视化测试")
    print("=" * 50)
    
    try:
        # 导入可视化模块
        from visualization.time_frequency_analysis import demonstrate_noise_resistance_analysis
        
        # 确保输出目录存在
        os.makedirs('output', exist_ok=True)
        
        # 运行完整的噪声抗性分析演示
        print("🚀 开始运行噪声抗性分析演示...")
        figures = demonstrate_noise_resistance_analysis()
        
        print("\n🎉 可视化生成完成！")
        print("\n📁 生成的文件:")
        print("  - output/time_frequency_analysis_comprehensive.png")
        print("  - output/detailed_comparison_analysis.png")
        
        print("\n📋 可视化内容说明:")
        print("  1. 综合分析图包含:")
        print("     • 时域信号对比（原始、增强、去噪）")
        print("     • 频域功率谱分析")
        print("     • 时频谱图（STFT）")
        print("  2. 详细对比图包含:")
        print("     • 信号重叠对比")
        print("     • 噪声分量分析")
        print("     • 频谱对比")
        print("     • 性能指标雷达图")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保所有依赖包已安装:")
        print("  pip install numpy matplotlib scipy seaborn")
        return False
        
    except Exception as e:
        print(f"❌ 运行错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_individual_components():
    """测试各个组件功能"""
    print("\n🔧 测试各个组件功能...")
    
    try:
        from visualization.time_frequency_analysis import (
            generate_synthetic_attack_signal,
            apply_data_augmentation,
            apply_model_denoising,
            compute_frequency_analysis
        )
        
        # 测试信号生成
        print("  ✓ 测试信号生成...")
        signal_data = generate_synthetic_attack_signal(length=500, sampling_rate=50)
        assert signal_data['signals'].shape == (500, 4)
        print(f"    生成信号形状: {signal_data['signals'].shape}")
        
        # 测试数据增强
        print("  ✓ 测试数据增强...")
        augmented = apply_data_augmentation(signal_data, 'gaussian_blur')
        assert augmented['signals'].shape == signal_data['signals'].shape
        print(f"    增强信号形状: {augmented['signals'].shape}")
        
        # 测试模型去噪
        print("  ✓ 测试模型去噪...")
        denoised = apply_model_denoising(signal_data, 'adaptive_filter')
        assert denoised['signals'].shape == signal_data['signals'].shape
        print(f"    去噪信号形状: {denoised['signals'].shape}")
        
        # 测试频域分析
        print("  ✓ 测试频域分析...")
        freq_analysis = compute_frequency_analysis(signal_data)
        assert 'freqs' in freq_analysis and 'psd' in freq_analysis
        print(f"    频域分析包含: {list(freq_analysis.keys())}")
        
        print("  ✅ 所有组件测试通过！")
        return True
        
    except Exception as e:
        print(f"  ❌ 组件测试失败: {e}")
        return False

def check_dependencies():
    """检查依赖包"""
    print("🔍 检查依赖包...")
    
    required_packages = [
        'numpy', 'matplotlib', 'scipy', 'seaborn'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"  ✓ {package}")
        except ImportError:
            print(f"  ❌ {package} (缺失)")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️ 缺失依赖包: {', '.join(missing_packages)}")
        print("请运行以下命令安装:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    else:
        print("✅ 所有依赖包已安装")
        return True

if __name__ == "__main__":
    print("🎯 时间/频域变换分析可视化测试脚本")
    print("=" * 60)
    
    # 检查依赖
    if not check_dependencies():
        sys.exit(1)
    
    # 测试组件
    if not test_individual_components():
        print("⚠️ 组件测试失败，但继续运行主程序...")
    
    # 运行主程序
    success = main()
    
    if success:
        print("\n🎉 测试完成！可视化文件已生成。")
        sys.exit(0)
    else:
        print("\n❌ 测试失败！")
        sys.exit(1)
