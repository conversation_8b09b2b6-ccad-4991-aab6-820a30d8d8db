# 时间/频域变换分析可视化模块

## 概述

本模块提供了深度学习模型动态抗噪能力的综合可视化分析，通过时域和频域的对比展示模型在信号处理中的去噪效果和特征保持能力。

## 功能特性

### 🎯 核心功能
- **合成攻击信号生成**: 创建包含正常信号、攻击成分和噪声的复合信号
- **数据增强技术**: 实现高斯模糊、时间抖动、幅度缩放等增强方法
- **模型去噪模拟**: 模拟深度学习模型的自适应滤波和频谱减法去噪
- **时频域分析**: 提供时域波形、功率谱密度、时频谱图的综合分析

### 📊 可视化输出
1. **综合分析图** (`time_frequency_analysis_comprehensive.png`)
   - 2×3 子图布局
   - 时域信号对比（上排）
   - 频域分析对比（下排）
   - 包含原始、增强、去噪三个阶段

2. **详细对比图** (`detailed_comparison_analysis.png`)
   - 信号重叠对比
   - 噪声分量分析
   - 频谱对比
   - 性能指标雷达图

## 技术规格

### 信号处理技术
- **时域分析**: 多维时间序列信号处理
- **频域分析**: FFT、STFT、功率谱密度计算
- **滤波技术**: Butterworth低通滤波、自适应滤波
- **窗函数**: Hann、Hamming、Blackman窗函数支持

### 数据增强方法
- **高斯模糊**: 保持核心频率特征，平滑高频噪声
- **时间抖动**: 轻微时间偏移，增强时间鲁棒性
- **幅度缩放**: 保持频率特征，调整信号幅度
- **组合增强**: 多种技术的智能组合

### 去噪算法
- **自适应滤波**: 基于Butterworth滤波器的自适应去噪
- **频谱减法**: 基于噪声功率谱估计的频域去噪
- **信号重构**: 保持原始信号动态特性的重构方法

## 使用方法

### 基本使用
```python
from src.visualization.time_frequency_analysis import demonstrate_noise_resistance_analysis

# 运行完整的分析演示
figures = demonstrate_noise_resistance_analysis()
```

### 自定义参数
```python
from src.visualization.time_frequency_analysis import (
    generate_synthetic_attack_signal,
    apply_data_augmentation,
    apply_model_denoising,
    create_comprehensive_visualization
)

# 生成自定义信号
signal_data = generate_synthetic_attack_signal(
    length=1000, 
    sampling_rate=100, 
    noise_level=0.4
)

# 应用增强
augmented = apply_data_augmentation(signal_data, 'combined')

# 应用去噪
denoised = apply_model_denoising(signal_data, 'adaptive_filter')

# 创建可视化
fig = create_comprehensive_visualization(signal_data, augmented, denoised)
```

### 快速测试
```bash
# 运行测试脚本
python test_time_frequency_visualization.py
```

## 输出解释

### 时域分析
- **原始信号**: 显示含噪攻击数据的时域特征
- **增强信号**: 展示数据增强后保持的核心特征
- **去噪信号**: 显示模型重构后的清洁信号

### 频域分析
- **功率谱密度**: 展示各频率成分的能量分布
- **主要频率标注**: 自动标注主要频率峰值
- **噪声抑制效果**: 对比去噪前后的频谱变化

### 时频分析
- **STFT谱图**: 展示信号的时频特性
- **动态频率变化**: 显示不同时间段的频率成分
- **噪声演化过程**: 展示噪声在时频域的分布

## 性能指标

### 量化分析指标
- **信噪比 (SNR)**: 衡量信号质量改善程度
- **均方误差 (MSE)**: 评估重构精度
- **相关系数**: 测量信号相似度
- **频率保持度**: 评估主要频率成分的保持程度
- **噪声抑制率**: 量化高频噪声的抑制效果

### 可视化特性
- **多维信号支持**: 处理多传感器时间序列数据
- **自适应颜色方案**: 根据信号类型自动调整颜色
- **中文字体支持**: 完整的中文标注和说明
- **高分辨率输出**: 300 DPI的出版质量图像

## 依赖要求

```
numpy >= 1.19.0
matplotlib >= 3.3.0
scipy >= 1.5.0
seaborn >= 0.11.0
```

## 文件结构

```
src/visualization/
├── time_frequency_analysis.py          # 主要可视化模块
├── README_time_frequency_analysis.md   # 本文档
└── __init__.py                         # 包初始化文件

test_time_frequency_visualization.py    # 测试脚本

output/                                 # 输出目录
├── time_frequency_analysis_comprehensive.png
└── detailed_comparison_analysis.png
```

## 扩展功能

### 自定义信号类型
可以通过修改 `generate_synthetic_attack_signal` 函数来支持不同类型的攻击信号：
- DoS攻击信号
- 数据重放攻击
- 位置欺骗攻击
- 速度异常信号

### 高级去噪算法
支持集成更复杂的去噪算法：
- 小波去噪
- 卡尔曼滤波
- 深度学习去噪网络
- 自编码器重构

### 实时分析
可扩展为实时信号分析：
- 流式数据处理
- 在线去噪
- 实时可视化更新
- 异常检测告警

## 注意事项

1. **内存使用**: 长时间序列可能消耗大量内存，建议分段处理
2. **计算复杂度**: STFT计算对于长信号可能较慢，可调整窗口参数
3. **字体设置**: 如果中文显示异常，请检查系统字体配置
4. **依赖版本**: 确保所有依赖包版本兼容

## 贡献指南

欢迎提交改进建议和bug报告。主要改进方向：
- 新的信号生成模式
- 更多去噪算法
- 交互式可视化
- 性能优化

## 许可证

本模块遵循项目整体许可证。
